import pandas as pd
import json
import re
from difflib import SequenceMatcher

def similarity(a, b):
    """计算两个字符串的相似度"""
    return SequenceMatcher(None, a, b).ratio()

def extract_keywords(text):
    """从文本中提取关键词（路名、地名等）"""
    if pd.isna(text) or text == '':
        return []
    
    # 移除常见的前缀和后缀
    text = str(text)
    text = re.sub(r'^(污水井_|雨水井_|雨水排出口_|污水排出口_)', '', text)
    text = re.sub(r'(东|西|南|北|口|井|段|号|米)$', '', text)
    
    # 提取路名关键词
    road_patterns = [
        r'([^_]+路)',
        r'([^_]+街)',
        r'([^_]+大道)',
        r'([^_]+巷)',
        r'([^_]+弄)',
        r'([^_]+园)',
        r'([^_]+村)',
        r'([^_]+区)',
        r'([^_]+站)',
        r'([^_]+桥)',
        r'([^_]+河)',
        r'([^_]+公园)',
        r'([^_]+学校)',
        r'([^_]+医院)',
        r'([^_]+广场)',
        r'([^_]+中心)',
    ]
    
    keywords = []
    for pattern in road_patterns:
        matches = re.findall(pattern, text)
        keywords.extend(matches)
    
    # 如果没有匹配到特定模式，则按分隔符分割
    if not keywords:
        parts = re.split(r'[_\-\s]+', text)
        keywords = [part for part in parts if len(part) > 1]
    
    return list(set(keywords))

def fuzzy_match(name_keywords, inspection_point, threshold=0.3):
    """模糊匹配关键词"""
    if pd.isna(inspection_point) or inspection_point == '':
        return False, 0
    
    inspection_point = str(inspection_point)
    max_similarity = 0
    
    for keyword in name_keywords:
        if keyword in inspection_point:
            return True, 1.0
        
        # 计算相似度
        sim = similarity(keyword, inspection_point)
        max_similarity = max(max_similarity, sim)
        
        # 检查部分匹配
        for i in range(len(inspection_point) - len(keyword) + 1):
            substring = inspection_point[i:i+len(keyword)]
            sim = similarity(keyword, substring)
            max_similarity = max(max_similarity, sim)
    
    return max_similarity >= threshold, max_similarity

def process_data():
    """处理数据并进行关联"""
    try:
        # 尝试不同的编码方式读取CSV文件
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        df = None
        
        for encoding in encodings:
            try:
                df = pd.read_csv('output11.csv', encoding=encoding)
                print(f"成功使用 {encoding} 编码读取文件")
                break
            except UnicodeDecodeError:
                continue
        
        if df is None:
            print("无法读取CSV文件，尝试所有编码都失败")
            return
        
        # 显示列名
        print("CSV文件列名:", df.columns.tolist())
        
        # 获取实际的列名（处理可能的编码问题）
        columns = df.columns.tolist()
        if len(columns) >= 4:
            id_col = columns[0]
            code_col = columns[1] 
            name_col = columns[2]
            inspection_col = columns[3]
        else:
            print("CSV文件格式不正确，列数不足")
            return
        
        print(f"使用列: {id_col}, {code_col}, {name_col}, {inspection_col}")
        
        # 显示前几行数据
        print("\n前5行数据:")
        print(df.head())
        
        # 进行关联匹配
        matched_data = []
        
        for index, row in df.iterrows():
            if pd.isna(row[inspection_col]) or row[inspection_col] == '':
                continue
                
            # 提取name字段的关键词
            name_keywords = extract_keywords(row[name_col])
            
            # 进行模糊匹配
            is_match, similarity_score = fuzzy_match(name_keywords, row[inspection_col])
            
            if is_match:
                matched_record = {
                    "id": row[id_col],
                    "code": row[code_col],
                    "name": row[name_col],
                    "巡检点位": row[inspection_col],
                    "匹配关键词": name_keywords,
                    "相似度": round(similarity_score, 3)
                }
                matched_data.append(matched_record)
                print(f"匹配成功: {row[code_col]} - {row[name_col]} -> {row[inspection_col]} (相似度: {similarity_score:.3f})")
        
        # 保存结果到JSON文件
        output_path = r'j:\自己项目原型\雨污口比对\关联.json'
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(matched_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n关联完成！共匹配到 {len(matched_data)} 条记录")
        print(f"结果已保存到: {output_path}")
        
        # 显示匹配统计
        total_records = len(df)
        match_rate = len(matched_data) / total_records * 100
        print(f"匹配率: {match_rate:.2f}% ({len(matched_data)}/{total_records})")
        
        return matched_data
        
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    process_data()
